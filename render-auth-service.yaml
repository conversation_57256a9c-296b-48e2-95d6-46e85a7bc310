services:
  - type: web
    name: factcheck-auth
    env: node
    buildCommand: cd services/auth-service && npm install
    startCommand: cd services/auth-service && npm start
    envVars:
      - key: PORT
        value: "10000"
      - key: NODE_ENV
        value: production
      - key: FIREBASE_PROJECT_ID
        sync: false
      - key: FIREBASE_PRIVATE_KEY
        sync: false
      - key: FIREBASE_CLIENT_EMAIL
        sync: false
      - key: FIREBASE_DATABASE_URL
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: REDIS_URL
        sync: false
