version: '3.8'

services:
  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - app-network
    restart: unless-stopped

  # Authentication Service
  auth-service:
    build:
      context: .
      dockerfile: services/auth-service/Dockerfile
    ports:
      - "3001:3001"
    env_file:
      - .env
    depends_on:
      - redis
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Link Verification Service
  link-service:
    build:
      context: .
      dockerfile: services/link-service/Dockerfile
    ports:
      - "3002:3002"
    env_file:
      - .env
    depends_on:
      - auth-service
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # News Service
  news-service:
    build:
      context: .
      dockerfile: services/news-service/Dockerfile
    ports:
      - "3003:3003"
    env_file:
      - .env
    depends_on:
      - auth-service
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Chat Service
  chat-service:
    build:
      context: .
      dockerfile: services/chat-service/Dockerfile
    ports:
      - "3004:3004"
    env_file:
      - .env
    depends_on:
      - auth-service
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Community Service
  community-service:
    build:
      context: .
      dockerfile: services/community-service/Dockerfile
    ports:
      - "3005:3005"
    env_file:
      - .env
    depends_on:
      - auth-service
      - redis
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin Service
  admin-service:
    build:
      context: .
      dockerfile: services/admin-service/Dockerfile
    ports:
      - "3006:3006"
    env_file:
      - .env
    depends_on:
      - auth-service
      - community-service
      - link-service
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3006/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    ports:
      - "8080:8080"
    env_file:
      - .env
    depends_on:
      - redis
      - auth-service
      - link-service
      - community-service
      - chat-service
      - news-service
      - admin-service
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Frontend
  frontend:
    build:
      context: client
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    env_file:
      - .env
    depends_on:
      - api-gateway
    networks:
      - app-network
    restart: unless-stopped

volumes:
  redis-data:

networks:
  app-network:
    driver: bridge
