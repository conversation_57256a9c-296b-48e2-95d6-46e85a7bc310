apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: anti-fraud-platform
  labels:
    app: auth-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        tier: backend
    spec:
      containers:
      - name: auth-service
        image: auth-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: NODE_ENV
        - name: PORT
          value: "3001"
        - name: FIREBASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: FIREBASE_PROJECT_ID
        - name: FIREBASE_PRIVATE_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: FIREBASE_PRIVATE_KEY
        - name: FIREBASE_CLIENT_EMAIL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: FIREBASE_CLIENT_EMAIL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: JWT_SECRET
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: shared-volume
          mountPath: /app/shared
      volumes:
      - name: shared-volume
        emptyDir: {}
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: anti-fraud-platform
  labels:
    app: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
  type: ClusterIP
